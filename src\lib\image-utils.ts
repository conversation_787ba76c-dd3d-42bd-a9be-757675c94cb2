/**
 * Image utility functions for handling image URLs and processing
 */

/**
 * Helper function to convert image paths to full URLs
 * @param imagePath - The relative image path from the API
 * @returns Full URL to the image
 */
export const getImageUrl = (imagePath: string): string => {
	const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
	// Remove leading slash if present to avoid double slashes
	const cleanPath = imagePath.startsWith('/') ? imagePath.slice(1) : imagePath;
	return `${baseUrl}/${cleanPath}`;
};

/**
 * Check if an image URL is valid and accessible
 * @param imageUrl - The image URL to validate
 * @returns Promise<boolean> - Whether the image is accessible
 */
export const validateImageUrl = async (imageUrl: string): Promise<boolean> => {
	try {
		const response = await fetch(imageUrl, { method: 'HEAD' });
		return response.ok;
	} catch {
		return false;
	}
};

/**
 * Get a fallback image URL for when the primary image fails to load
 * @param fallbackPath - Optional custom fallback path
 * @returns Fallback image URL
 */
export const getFallbackImageUrl = (fallbackPath: string = '/images/roommate1.png'): string => {
	return fallbackPath;
};

/**
 * Extract image filename from a full URL or path
 * @param imageUrl - The image URL or path
 * @returns The filename portion
 */
export const extractImageFilename = (imageUrl: string): string => {
	return imageUrl.split('/').pop() || '';
};

/**
 * Check if an image URL is from an external source
 * @param imageUrl - The image URL to check
 * @returns Whether the image is from an external source
 */
export const isExternalImage = (imageUrl: string): boolean => {
	try {
		const url = new URL(imageUrl);
		const currentDomain = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
		const currentUrl = new URL(currentDomain);
		return url.hostname !== currentUrl.hostname;
	} catch {
		// If URL parsing fails, assume it's a relative path (internal)
		return false;
	}
};
