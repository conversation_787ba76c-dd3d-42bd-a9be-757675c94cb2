"use client";

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AddressSelector, AddressData, formatAddress, isAddressComplete } from '@/components/ui/address-selector';
import { ImageUploadWithApi, UploadedImage } from '@/components/ui/image-upload-with-api';
import { Button } from '@/components/ui/button';
import { useReferenceStore } from '@/stores/referenceStore';

export default function TestAddressUploadPage() {
  const [address, setAddress] = useState<AddressData>({
    street: '',
    ward: null,
    district: null,
    province: null
  });
  
  const [images, setImages] = useState<UploadedImage[]>([]);
  const { enums, loadReferenceData } = useReferenceStore();

  const handleSubmit = () => {
    console.log('Address:', address);
    console.log('Formatted Address:', formatAddress(address));
    console.log('Is Complete:', isAddressComplete(address));
    console.log('Images:', images);
    console.log('Enums:', enums);
  };

  const loadEnums = () => {
    loadReferenceData();
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold">Test Address & Upload Components</h1>
        <p className="text-gray-600 mt-2">
          Test the new AddressSelector and ImageUploadWithApi components
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Address Selector */}
        <Card>
          <CardHeader>
            <CardTitle>Address Selector</CardTitle>
            <CardDescription>
              Select province, district, and ward using real API data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <AddressSelector
              value={address}
              onChange={setAddress}
              required
              showStreetInput
            />
            
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2">Current Address:</h4>
              <p className="text-sm text-gray-600">
                {formatAddress(address) || 'No address selected'}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Complete: {isAddressComplete(address) ? 'Yes' : 'No'}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Image Upload */}
        <Card>
          <CardHeader>
            <CardTitle>Image Upload with API</CardTitle>
            <CardDescription>
              Upload images to server using the upload API
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ImageUploadWithApi
              value={images}
              onChange={setImages}
              maxFiles={5}
              maxSize={5}
              uploadMode="bulk"
              autoUpload={true}
            />
            
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2">Uploaded Images:</h4>
              <div className="space-y-1">
                {images.map((image, index) => (
                  <div key={image.id} className="text-xs text-gray-600">
                    {index + 1}. {image.url ? 'Uploaded' : 'Uploading...'} 
                    {image.uploadError && ' (Error)'}
                  </div>
                ))}
                {images.length === 0 && (
                  <p className="text-xs text-gray-500">No images uploaded</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enums Test */}
      <Card>
        <CardHeader>
          <CardTitle>Reference Data (Enums)</CardTitle>
          <CardDescription>
            Test loading application enums from the API
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-4">
            <Button onClick={loadEnums}>Load Enums</Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {enums && Object.entries(enums).map(([key, values]) => (
              <div key={key} className="p-3 bg-gray-50 rounded-lg">
                <h5 className="font-medium text-sm mb-2 capitalize">
                  {key.replace(/([A-Z])/g, ' $1').trim()}
                </h5>
                <div className="space-y-1">
                  {values.slice(0, 3).map((value: string) => (
                    <div key={value} className="text-xs text-gray-600">
                      {value}
                    </div>
                  ))}
                  {values.length > 3 && (
                    <div className="text-xs text-gray-500">
                      +{values.length - 3} more...
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
          
          {!enums && (
            <p className="text-sm text-gray-500">Click "Load Enums" to fetch reference data</p>
          )}
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="text-center">
        <Button onClick={handleSubmit} size="lg">
          Log Data to Console
        </Button>
      </div>
    </div>
  );
}
